#!/usr/bin/env node

/**
 * Test Authentication Flow
 * Test the complete authentication flow from landing page to API server
 */

const http = require('http');

const LANDING_PAGE_URL = 'http://localhost:3004';
const API_BASE_URL = 'http://localhost:3005';

async function testAuthFlow() {
  console.log('🔐 Testing Authentication Flow...');
  console.log(`🌐 Landing Page: ${LANDING_PAGE_URL}`);
  console.log(`📡 API Server: ${API_BASE_URL}`);
  
  try {
    // Test 1: Landing page is accessible
    console.log('\n1️⃣ Testing Landing Page Accessibility...');
    const landingResult = await makeRequest(LANDING_PAGE_URL, '/');
    console.log('✅ Landing page is accessible');
    
    // Test 2: AI onboarding page is accessible
    console.log('\n2️⃣ Testing AI Onboarding Page...');
    const onboardingResult = await makeRequest(LANDING_PAGE_URL, '/ai-onboarding');
    console.log('✅ AI onboarding page is accessible');
    
    // Test 3: API conversation start endpoint (from landing page perspective)
    console.log('\n3️⃣ Testing AI Conversation API Endpoint...');
    const aiApiResult = await makeRequest(LANDING_PAGE_URL, '/api/ai/conversation/start', 'POST', {
      userRole: 'CLIENT',
      language: 'ar',
      sessionType: 'onboarding'
    });
    console.log('✅ AI conversation API endpoint is accessible');
    
    // Test 4: Direct API server health
    console.log('\n4️⃣ Testing Direct API Server Health...');
    const apiHealthResult = await makeRequest(API_BASE_URL, '/health');
    console.log('✅ API server is healthy');
    
    console.log('\n🎉 All authentication flow components are working!');
    console.log('\n📋 Summary:');
    console.log('✅ Landing page: Working');
    console.log('✅ AI onboarding page: Working');
    console.log('✅ AI conversation API: Working');
    console.log('✅ Backend API server: Working');
    console.log('\n🚀 The AI chat interface should now work properly!');
    console.log('\n🔗 Test URL: http://localhost:3004/ai-onboarding');
    
  } catch (error) {
    console.error('\n❌ Authentication Flow Test Failed:', error.message);
    console.log('\n🔧 Troubleshooting Steps:');
    console.log('1. Make sure both servers are running:');
    console.log('   - Landing page: cd apps/landing-page && npm run dev');
    console.log('   - API server: cd apps/api && npm run dev');
    console.log('2. Check the updated API_BASE_URL in apps/landing-page/.env.local');
    console.log('3. Restart the landing page server to pick up environment changes');
    process.exit(1);
  }
}

function makeRequest(baseUrl, path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, baseUrl);
    
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Auth-Flow-Test/1.0'
      },
      timeout: 10000
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        console.log(`   Status: ${res.statusCode} ${res.statusMessage}`);
        
        // Consider most responses as success (server is responding)
        // 401 is expected for unauthenticated requests
        if (res.statusCode < 500) {
          resolve({
            status: res.statusCode,
            data: responseData.substring(0, 100)
          });
        } else {
          reject(new Error(`Server error: ${res.statusCode} ${res.statusMessage}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(new Error(`Connection failed: ${error.message}`));
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (data && method !== 'GET') {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Run the test
testAuthFlow();
