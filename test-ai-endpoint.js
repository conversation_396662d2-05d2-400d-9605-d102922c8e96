#!/usr/bin/env node

/**
 * Test AI Endpoint Connection
 * Test the specific AI conversation endpoints that the landing page uses
 */

const http = require('http');

const API_BASE_URL = 'http://localhost:3005';

async function testAIEndpoints() {
  console.log('🤖 Testing AI Endpoints...');
  console.log(`📡 API Base URL: ${API_BASE_URL}`);
  
  try {
    // First test: Try to authenticate (should get 400/401 - expected)
    console.log('\n1️⃣ Testing NextAuth Login Endpoint...');
    const authResult = await makeRequest('/api/v1/auth/nextauth/login', 'POST', {
      email: '<EMAIL>',
      name: 'Test User',
      image: null,
      provider: 'google'
    });
    console.log('✅ Auth endpoint accessible');
    
    // Second test: Try AI conversation start (should get 401 - expected without auth)
    console.log('\n2️⃣ Testing AI Conversation Start Endpoint...');
    const aiResult = await makeRequest('/api/v1/ai/v2/conversation/start', 'POST', {
      userRole: 'CLIENT',
      language: 'ar',
      sessionType: 'onboarding',
      culturalContext: {
        location: 'سوريا',
        dialect: 'general'
      }
    });
    console.log('✅ AI conversation endpoint accessible');
    
    console.log('\n🎉 All AI endpoints are accessible!');
    console.log('✅ The landing page should now be able to connect to the API server.');
    console.log('\n🔄 Please restart the landing page development server to pick up the new API_BASE_URL.');
    
  } catch (error) {
    console.error('\n❌ AI Endpoint Test Failed:', error.message);
    console.log('\n🔧 Troubleshooting Steps:');
    console.log('1. Verify the API server is running on port 3005');
    console.log('2. Check the AI routes are properly configured');
    console.log('3. Verify OpenRouter API key is set in apps/api/.env');
    process.exit(1);
  }
}

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, API_BASE_URL);
    
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'AI-Endpoint-Test/1.0'
      },
      timeout: 10000
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        console.log(`   Status: ${res.statusCode} ${res.statusMessage}`);
        
        // For auth endpoints, 400/401 is expected (no valid auth)
        // For AI endpoints, 401 is expected (authentication required)
        if (res.statusCode < 500) {
          try {
            const jsonData = JSON.parse(responseData);
            console.log(`   Response: ${JSON.stringify(jsonData).substring(0, 100)}...`);
          } catch (e) {
            console.log(`   Response: ${responseData.substring(0, 100)}...`);
          }
          resolve({
            status: res.statusCode,
            data: responseData
          });
        } else {
          reject(new Error(`Server error: ${res.statusCode} ${res.statusMessage}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(new Error(`Connection failed: ${error.message}`));
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (data && method !== 'GET') {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Run the test
testAIEndpoints();
