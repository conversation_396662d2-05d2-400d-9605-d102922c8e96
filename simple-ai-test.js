/**
 * Simple AI endpoint test
 */

const axios = require('axios');

const API_URL = 'http://localhost:3005';

async function testSimpleAI() {
  console.log('🧪 Simple AI Test\n');

  try {
    // Test 1: Health check
    console.log('1️⃣ Health check...');
    const health = await axios.get(`${API_URL}/health`);
    console.log('✅ Health:', health.data.status);

    // Test 2: Test connection
    console.log('\n2️⃣ Test connection...');
    const connection = await axios.get(`${API_URL}/api/v1/test-connection`);
    console.log('✅ Connection test passed');
    console.log('OpenRouter:', connection.data.openrouter.apiKey);

    // Test 3: AI conversation start with X-User-ID
    console.log('\n3️⃣ AI conversation start...');
    const aiResponse = await axios.post(`${API_URL}/api/v1/ai/conversation/start`, {
      userRole: 'EXPERT',
      language: 'ar',
      sessionType: 'onboarding'
    }, {
      headers: {
        'X-User-ID': 'test-user-123',
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ AI conversation started!');
    console.log('Session ID:', aiResponse.data.data.sessionId);
    console.log('Status:', aiResponse.data.data.status);
    
    if (aiResponse.data.data.messages && aiResponse.data.data.messages.length > 0) {
      console.log('Welcome message:', aiResponse.data.data.messages[0].content.substring(0, 100) + '...');
    }

    console.log('\n🎉 AI endpoints are working!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testSimpleAI().catch(console.error);
